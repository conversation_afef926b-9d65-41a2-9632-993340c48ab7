import React, { useState } from 'react';
import InventoryComponent from './components/inventory';
import useNuiEvent from './hooks/useNuiEvent';
import { Items } from './store/items';
import { Locale } from './store/locale';
import { setImagePath } from './store/imagepath';
import { setupInventory, setEquipmentItem } from './store/inventory';
import { Inventory } from './typings';
import { useAppDispatch } from './store';
import { debugData } from './utils/debugData';
import DragPreview from './components/utils/DragPreview';
import { fetchNui } from './utils/fetchNui';
import { useDragDropManager } from 'react-dnd';
import KeyPress from './components/utils/KeyPress';

debugData([
  {
    action: 'setupInventory',
    data: {
      leftInventory: {
        id: 'test',
        type: 'player',
        slots: 50,
        label: '<PERSON>',
        weight: 3000,
        maxWeight: 5000,
        items: [
          {
            slot: 1,
            name: 'iron',
            weight: 3000,
            metadata: {
              description: `name: <PERSON><PERSON><PERSON><PERSON>  \n Gender: Male`,
              ammo: 3,
              mustard: '60%',
              ketchup: '30%',
              mayo: '10%',
            },
            count: 5,
          },
          { slot: 2, name: 'joint', weight: 0, count: 1, metadata: { durability: 75 } },
          { slot: 3, name: 'copper', weight: 100, count: 12, metadata: { type: 'Special' } },
          {
            slot: 4,
            name: 'water',
            weight: 100,
            count: 1,
            metadata: { description: 'Generic item description' },
          },
          { slot: 5, name: 'water', weight: 100, count: 1 },
          {
            slot: 6,
            name: 'backwoods',
            weight: 100,
            count: 1,
            metadata: {
              label: 'Russian Cream',
              imageurl: 'https://i.imgur.com/2xHhTTz.png',
            },
          },
          { slot: 7, name: 'iron', weight: 0, count: 7, metadata: { durability: 75 } },
        ],
      },
      rightInventory: {
        id: 'shop',
        type: 'crafting',
        slots: 5000,
        label: 'Bob Smith',
        weight: 3000,
        maxWeight: 5000,
        items: [
          {
            slot: 1,
            name: 'lockpick',
            weight: 500,
            price: 300,
            ingredients: {
              iron: 5,
              copper: 5,
              joint: 0.1,
            },
            metadata: {
              description: 'Simple lockpick that breaks easily and can pick basic door locks',
            },
          },
        ],
      },
    },
  },
]);

const App: React.FC = () => {
  const dispatch = useAppDispatch();
  const manager = useDragDropManager();
  const [inventoryVisible, setInventoryVisible] = useState(false);

  useNuiEvent<{
    locale: { [key: string]: string };
    items: typeof Items;
    leftInventory: Inventory;
    imagepath: string;
    equipment?: { mask: any; vest: any; bag: any };
  }>('init', ({ locale, items, leftInventory, imagepath, equipment }) => {
    for (const name in locale) Locale[name] = locale[name];
    for (const name in items) Items[name] = items[name];

    setImagePath(imagepath);
    dispatch(setupInventory({ leftInventory }));

    if (equipment && typeof equipment === 'object' && !Array.isArray(equipment)) {
      console.log('Initializing equipment:', equipment);
      dispatch(setEquipmentItem({ slot: 'mask', item: equipment.mask || null }));
      dispatch(setEquipmentItem({ slot: 'vest', item: equipment.vest || null }));
      dispatch(setEquipmentItem({ slot: 'bag', item: equipment.bag || null }));
    } else {
      console.log('Invalid equipment data, initializing empty:', equipment);
      dispatch(setEquipmentItem({ slot: 'mask', item: null }));
      dispatch(setEquipmentItem({ slot: 'vest', item: null }));
      dispatch(setEquipmentItem({ slot: 'bag', item: null }));
    }
  });

  useNuiEvent<{
    leftInventory?: any;
    rightInventory?: any;
  }>('setupInventory', () => {
    setInventoryVisible(true);
  });

  fetchNui('uiLoaded', {});

  useNuiEvent('closeInventory', () => {
    manager.dispatch({ type: 'dnd-core/END_DRAG' });
    setInventoryVisible(false);
  });

  useNuiEvent<boolean>('setInventoryVisible', setInventoryVisible);

  useNuiEvent<{ mask: any; vest: any; bag: any }>('updateEquipment', (equipment) => {
    console.log('Equipment update received:', JSON.stringify(equipment, null, 2));
    if (equipment && typeof equipment === 'object' && !Array.isArray(equipment)) {
      dispatch(setEquipmentItem({ slot: 'mask', item: equipment.mask || null }));
      dispatch(setEquipmentItem({ slot: 'vest', item: equipment.vest || null }));
      dispatch(setEquipmentItem({ slot: 'bag', item: equipment.bag || null }));
    }
  });

  return (
    <div className="app-wrapper">
      <InventoryComponent />
      <DragPreview />
      <KeyPress />
      <div className="player-image-container">

      </div>

    </div>
  );
};

addEventListener("dragstart", function(event) {
  event.preventDefault()
})

export default App;
