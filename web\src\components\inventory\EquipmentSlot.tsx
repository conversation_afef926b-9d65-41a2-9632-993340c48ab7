import React, { useCallback, useRef } from 'react';
import { DragSource, Slot, SlotWithItem } from '../../typings';
import { useDrag, useDrop } from 'react-dnd';
import { useAppDispatch } from '../../store';
import WeightBar from '../utils/WeightBar';
import { onDrop } from '../../dnd/onDrop';
import { Items } from '../../store/items';
import { getItemUrl, isSlotWithItem } from '../../helpers';
import { onUse } from '../../dnd/onUse';
import { Locale } from '../../store/locale';
import { openTooltip, closeTooltip } from '../../store/tooltip';
import { openContextMenu } from '../../store/contextMenu';
import { setEquipmentItem, removeEquipmentItem } from '../../store/inventory';
import { fetchNui } from '../../utils/fetchNui';

interface Props {
  slotType: 'mask' | 'vest' | 'bag';
  item: Slot | null;
}

const slotLabels = {
  mask: 'Mask',
  vest: 'Armor',
  bag: 'Bag'
};

const EquipmentSlot: React.FC<Props> = ({ slotType, item }) => {
  const dispatch = useAppDispatch();
  const ref = useRef<HTMLDivElement>(null);

  // Safety check for item data
  const safeItem = item && typeof item === 'object' && item.name ? item : null;

  const handleContext = useCallback(
    (event: React.MouseEvent<HTMLDivElement>) => {
      if (!safeItem || !isSlotWithItem(safeItem)) return;
      event.preventDefault();

      // For vest equipment slots, trigger next-kevlar vest management
      if (slotType === 'vest') {
        console.log('Opening next-kevlar vest management for slot:', safeItem.slot);
        fetchNui('openVestManagement', {
          slot: safeItem.slot,
          item: safeItem
        });
      } else {
        // For other equipment slots, open regular context menu
        dispatch(
          openContextMenu({
            item: safeItem as SlotWithItem,
            coords: { x: event.clientX, y: event.clientY },
          })
        );
      }
    },
    [safeItem, dispatch, slotType]
  );

  const handleClick = useCallback(() => {
    if (!safeItem || !isSlotWithItem(safeItem)) return;
    onUse({ name: safeItem.name, slot: safeItem.slot });
  }, [safeItem]);

  const [{ isDragging }, drag] = useDrag<DragSource, void, { isDragging: boolean }>(
    () => ({
      type: 'SLOT',
      collect: (monitor) => ({
        isDragging: monitor.isDragging(),
      }),
      item: () =>
        item && isSlotWithItem(item)
          ? {
              item: {
                slot: item.slot,
                name: item.name,
                metadata: { ...item.metadata, slotType: slotType }
              },
              inventory: 'equipment',
              inventoryType: 'equipment',
              image: getItemUrl(item as SlotWithItem),
            }
          : null,
      canDrag: () => item !== null && isSlotWithItem(item),
    }),
    [item]
  );

  const [{ isOver }, drop] = useDrop<DragSource, void, { isOver: boolean }>(
    () => ({
      accept: 'SLOT',
      collect: (monitor) => ({
        isOver: monitor.isOver(),
      }),
      drop: (source) => {
        dispatch(closeTooltip());
        console.log('🎯 EQUIPMENT DROP DETECTED:', JSON.stringify({
          source: source,
          targetSlot: slotType,
          itemName: source.item.name
        }, null, 2));
        // Handle equipment drop logic
        handleEquipmentDrop(source, slotType);
      },
      canDrop: (source) => {
        const itemName = source.item.name.toLowerCase();

        console.log('Equipment canDrop check:', JSON.stringify({
          slotType,
          itemName,
          sourceItem: source.item
        }, null, 2));

        // Allow clothing items and specific item types
        switch (slotType) {
          case 'mask':
            return itemName.includes('mask') || itemName.includes('helmet') ||
                   itemName.includes('face') || itemName.includes('balaclava') ||
                   itemName.includes('clothing_mask') || itemName.includes('glasses');
          case 'vest':
            // Very permissive for next-kevlar compatibility
            const isVestItem = itemName.includes('vest') || itemName.includes('armor') ||
                              itemName.includes('bulletproof') || itemName.includes('kevlar') ||
                              itemName.includes('clothing_vest') || itemName.includes('plate') ||
                              itemName.includes('carrier') || itemName.includes('tactical') ||
                              // Common next-kevlar item patterns
                              itemName.includes('heavy') || itemName.includes('light') ||
                              itemName.includes('medium') || itemName.includes('protection');

            console.log('Vest item check:', JSON.stringify({ itemName, isVestItem }, null, 2));
            return isVestItem;
          case 'bag':
            return itemName.includes('bag') || itemName.includes('backpack') ||
                   itemName.includes('parachute') || itemName.includes('clothing_bag');
          default:
            return false;
        }
      },
    }),
    [slotType, dispatch]
  );

  const handleEquipmentDrop = (source: DragSource, targetSlot: string) => {
    console.log('Handling equipment drop:', JSON.stringify({
      fromSlot: source.item.slot,
      fromInventory: source.inventory,
      toSlot: targetSlot,
      item: source.item
    }, null, 2));

    // Send to server to handle the equipment change
    const equipData = {
      fromSlot: source.item.slot,
      fromInventory: source.inventory,
      toSlot: targetSlot,
      item: source.item
    };

    console.log('Sending equipment data to server:', JSON.stringify(equipData, null, 2));

    fetchNui('equipItem', equipData).then(result => {
      console.log('Equipment result:', result);
    }).catch(error => {
      console.error('Equipment error:', error);
    });
  };

  drag(drop(ref));

  const handleMouseEnter = useCallback(() => {
    if (!item || !isSlotWithItem(item)) return;
    dispatch(
      openTooltip({
        item: item as SlotWithItem,
        inventoryType: 'equipment',
      })
    );
  }, [item, dispatch]);

  const handleMouseLeave = useCallback(() => {
    dispatch(closeTooltip());
  }, [dispatch]);

  return (
    <div
      ref={ref}
      onContextMenu={handleContext}
      onClick={handleClick}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      className="equipment-slot"
      style={{
        opacity: isDragging ? 0.4 : 1.0,
        backgroundImage: `url(${item?.name ? getItemUrl(item as SlotWithItem) : 'none'}`,
        border: isOver ? '1px dashed rgba(255,255,255,0.4)' : '',
      }}
    >
      {safeItem && isSlotWithItem(safeItem) && safeItem.name ? (
        <div className="item-slot-wrapper">
          <div className="item-slot-header-wrapper">
            <div className="item-slot-info-wrapper">
              <p>
                {safeItem.weight > 0
                  ? safeItem.weight >= 1000
                    ? `${(safeItem.weight / 1000).toLocaleString('en-us', {
                        minimumFractionDigits: 2,
                      })}kg `
                    : `${safeItem.weight.toLocaleString('en-us', {
                        minimumFractionDigits: 0,
                      })}g `
                  : ''}
              </p>
              <p>{safeItem.count ? safeItem.count.toLocaleString('en-us') + `x` : ''}</p>
            </div>
          </div>
          <div>
            {safeItem?.durability !== undefined && (
              <WeightBar percent={safeItem.durability} durability />
            )}
            <div className="inventory-slot-label-box">
              <div className="inventory-slot-label-text">
                {safeItem.metadata?.label ? safeItem.metadata.label : Items[safeItem.name]?.label || safeItem.name}
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="equipment-slot-placeholder">
          {slotLabels[slotType]}
        </div>
      )}
    </div>
  );
};

export default EquipmentSlot;
