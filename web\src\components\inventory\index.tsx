import React, { useState } from 'react';
import useNuiEvent from '../../hooks/useNuiEvent';
import InventoryControl from './InventoryControl';
import InventoryHotbar from './InventoryHotbar';
import HotslotInventory from './HotslotInventory';
import { useAppDispatch, useAppSelector } from '../../store';
import { refreshSlots, setAdditionalMetadata, setupInventory, selectEquipment } from '../../store/inventory';
import { useExitListener } from '../../hooks/useExitListener';
import type { Inventory as InventoryProps } from '../../typings';
import RightInventory from './RightInventory';
import LeftInventory from './LeftInventory';
import Tooltip from '../utils/Tooltip';
import { closeTooltip } from '../../store/tooltip';
import InventoryContext from './InventoryContext';
import { closeContextMenu } from '../../store/contextMenu';
import Fade from '../utils/transitions/Fade';
import EquipmentSlot from './EquipmentSlot';

const Inventory: React.FC = () => {
  const [inventoryVisible, setInventoryVisible] = useState(false);
  const dispatch = useAppDispatch();
  const equipment = useAppSelector(selectEquipment);

  useNuiEvent<boolean>('setInventoryVisible', setInventoryVisible);
  useNuiEvent<false>('closeInventory', () => {
    setInventoryVisible(false);
    dispatch(closeContextMenu());
    dispatch(closeTooltip());
  });
  useExitListener(setInventoryVisible);

  useNuiEvent<{
    leftInventory?: InventoryProps;
    rightInventory?: InventoryProps;
  }>('setupInventory', (data) => {
    dispatch(setupInventory(data));
    !inventoryVisible && setInventoryVisible(true);
  });

  useNuiEvent('refreshSlots', (data) => dispatch(refreshSlots(data)));

  useNuiEvent('displayMetadata', (data: Array<{ metadata: string; value: string }>) => {
    dispatch(setAdditionalMetadata(data));
  });

  return (
    <>
      <Fade in={inventoryVisible}>
        <div className="inventory-wrapper">
        <div className="hotslot-container">
            <HotslotInventory/>
          </div>
          <div className="inventory-wrapper-container">
            <LeftInventory />
            <InventoryControl />
            <RightInventory />
            <Tooltip />
            <InventoryContext />
          </div>
        </div>
      </Fade>
      {/* Equipment slots - only visible when inventory is open */}
      {inventoryVisible && (
        <div className="equipment-container">
          <EquipmentSlot slotType="mask" item={equipment.mask} />
          <EquipmentSlot slotType="vest" item={equipment.vest} />
          <EquipmentSlot slotType="bag" item={equipment.bag} />
        </div>
      )}
      <InventoryHotbar />
    </>
  );
};

export default Inventory;
